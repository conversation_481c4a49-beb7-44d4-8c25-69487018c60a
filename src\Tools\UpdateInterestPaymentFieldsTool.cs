using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.Tools;

/// <summary>
/// Tool to update existing RZW_SAVINGS_INTEREST_PAYMENT records with missing fields
/// </summary>
public class UpdateInterestPaymentFieldsTool
{
    private readonly AppDbContext _context;

    public UpdateInterestPaymentFieldsTool(AppDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Updates all existing interest payment records with missing PRINCIPAL_AMOUNT, ACCUMULATED_INTEREST, and DAILY_RATE
    /// </summary>
    public async Task<int> UpdateMissingFieldsAsync()
    {
        Console.WriteLine("Starting update of missing Interest Payment fields...");
        
        // Get all interest payments with missing fields
        var paymentsToUpdate = await _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .ThenInclude(a => a.RzwSavingsPlan)
            .Where(p => p.PrincipalAmount == null || 
                       p.AccumulatedInterest == null || 
                       p.DailyRate == null)
            .OrderBy(p => p.RzwSavingsAccountId)
            .ThenBy(p => p.PaymentDate)
            .ToListAsync();

        Console.WriteLine($"Found {paymentsToUpdate.Count} interest payments to update");

        if (paymentsToUpdate.Count == 0)
        {
            Console.WriteLine("No payments need updating.");
            return 0;
        }

        int updatedCount = 0;
        var accountGroups = paymentsToUpdate.GroupBy(p => p.RzwSavingsAccountId);

        foreach (var accountGroup in accountGroups)
        {
            var accountId = accountGroup.Key;
            var payments = accountGroup.OrderBy(p => p.PaymentDate).ToList();
            
            // Get account info
            var account = payments.First().RzwSavingsAccount;
            var plan = account.RzwSavingsPlan;
            
            Console.WriteLine($"Processing Account ID: {accountId}, Plan: {plan?.Name ?? "Unknown"}");

            decimal cumulativeInterest = 0;

            foreach (var payment in payments)
            {
                bool updated = false;

                // Update PRINCIPAL_AMOUNT
                if (payment.PrincipalAmount == null)
                {
                    payment.PrincipalAmount = account.RzwAmount;
                    updated = true;
                }

                // Update ACCUMULATED_INTEREST
                cumulativeInterest += payment.RzwAmount;
                if (payment.AccumulatedInterest == null)
                {
                    payment.AccumulatedInterest = cumulativeInterest;
                    updated = true;
                }

                // Update DAILY_RATE
                if (payment.DailyRate == null)
                {
                    if (plan != null)
                    {
                        payment.DailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(
                            plan.InterestRate, plan.TermType);
                    }
                    else if (!string.IsNullOrEmpty(account.TermType))
                    {
                        // Fallback to account's interest rate if plan is not available
                        payment.DailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(
                            account.InterestRate, account.TermType);
                    }
                    updated = true;
                }

                if (updated)
                {
                    updatedCount++;
                    Console.WriteLine($"  Updated Payment ID: {payment.Id}, Date: {payment.PaymentDate:yyyy-MM-dd}, " +
                                    $"Principal: {payment.PrincipalAmount:N8}, " +
                                    $"Accumulated: {payment.AccumulatedInterest:N8}, " +
                                    $"Daily Rate: {payment.DailyRate:N8}");
                }
            }
        }

        // Save changes
        await _context.SaveChangesAsync();
        
        Console.WriteLine($"Successfully updated {updatedCount} interest payment records");
        return updatedCount;
    }

    /// <summary>
    /// Shows a summary of interest payments before and after update
    /// </summary>
    public async Task ShowSummaryAsync()
    {
        var payments = await _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .OrderBy(p => p.RzwSavingsAccountId)
            .ThenBy(p => p.PaymentDate)
            .ToListAsync();

        Console.WriteLine("\n=== Interest Payment Summary ===");
        Console.WriteLine($"Total Payments: {payments.Count}");
        
        var missingPrincipal = payments.Count(p => p.PrincipalAmount == null);
        var missingAccumulated = payments.Count(p => p.AccumulatedInterest == null);
        var missingDailyRate = payments.Count(p => p.DailyRate == null);
        
        Console.WriteLine($"Missing Principal Amount: {missingPrincipal}");
        Console.WriteLine($"Missing Accumulated Interest: {missingAccumulated}");
        Console.WriteLine($"Missing Daily Rate: {missingDailyRate}");

        if (payments.Any())
        {
            Console.WriteLine("\nSample Records:");
            foreach (var payment in payments.Take(5))
            {
                Console.WriteLine($"ID: {payment.Id}, Account: {payment.RzwSavingsAccountId}, " +
                                $"Amount: {payment.RzwAmount:N8}, " +
                                $"Principal: {payment.PrincipalAmount?.ToString("N8") ?? "NULL"}, " +
                                $"Accumulated: {payment.AccumulatedInterest?.ToString("N8") ?? "NULL"}, " +
                                $"Daily Rate: {payment.DailyRate?.ToString("N8") ?? "NULL"}");
            }
        }
    }
}
