using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Main service for RZW savings account operations
/// </summary>
public class RzwSavingsService : IRzwSavingsService
{
    private readonly AppDbContext _context;
    private readonly IRzwBalanceManagementService _balanceService;
    private readonly IRzwSavingsPlanService _planService;
    private readonly IRzwSavingsInterestService _interestService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<RzwSavingsService> _logger;

    public RzwSavingsService(
        AppDbContext context,
        IRzwBalanceManagementService balanceService,
        IRzwSavingsPlanService planService,
        IRzwSavingsInterestService interestService,
        ITokenPriceService tokenPriceService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<RzwSavingsService> logger)
    {
        _context = context;
        _balanceService = balanceService;
        _planService = planService;
        _interestService = interestService;
        _tokenPriceService = tokenPriceService;
        _localizer = localizer;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new savings account
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="planId">Plan ID</param>
    /// <param name="rzwAmount">RZW amount to deposit</param>
    /// <param name="autoRenew">Auto-renew option</param>
    /// <returns>Success status, message, and created account</returns>
    public async Task<(bool Success, string Message, RzwSavingsAccount? Account)> CreateSavingsAccountAsync(
        int userId, int planId, decimal rzwAmount, bool autoRenew = false)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Plan validation
            var plan = await _planService.GetPlanByIdAsync(planId);
            if (plan == null)
                return (false, _localizer["Invalid savings plan"].Value, null);

            // Validate plan has valid interest rate
            if (plan.InterestRate <= 0)
            {
                _logger.LogError("Attempted to create savings account with plan {PlanId} that has zero or negative interest rate: {InterestRate}",
                    planId, plan.InterestRate);
                return (false, _localizer["Selected savings plan has invalid interest rate"].Value, null);
            }

            // Plan amount validation
            if (!await _planService.ValidatePlanAsync(planId, rzwAmount))
                return (false, _localizer["Amount does not meet plan requirements"].Value, null);

            // Balance check
            if (!await _balanceService.HasSufficientAvailableRzwAsync(userId, rzwAmount))
                return (false, _localizer["Insufficient available RZW balance"].Value, null);

            // Create savings account first
            var startDate = DateTime.UtcNow;
            var maturityDate = CalculateMaturityDate(startDate, plan.TermType, plan.TermDuration);

            var savingsAccount = new RzwSavingsAccount
            {
                UserId = userId,
                PlanId = planId,
                RzwAmount = rzwAmount,
                InterestRate = plan.InterestRate,
                TermType = plan.TermType,
                TermDuration = plan.TermDuration,
                StartDate = startDate,
                MaturityDate = maturityDate,
                Status = RzwSavingsStatus.Active,
                AutoRenew = autoRenew,
                EarlyWithdrawalPenalty = RzwSavingsConstants.DEFAULT_EARLY_WITHDRAWAL_PENALTY,
                CreatedDate = DateTime.UtcNow
            };

            _context.RzwSavingsAccounts.Add(savingsAccount);
            await _context.SaveChangesAsync(); // Save to get the ID

            // Lock RZW balance using existing transaction with savings account ID
            var lockSuccess = await _balanceService.LockRzwForSavingsAsync(userId, rzwAmount,
                $"RZW Savings Account - {plan.Name}", _context, savingsAccount.Id);

            if (!lockSuccess)
                return (false, _localizer["Failed to lock RZW balance"].Value, null);

            await transaction.CommitAsync();

            _logger.LogInformation("RZW savings account created. UserId: {UserId}, Amount: {Amount}, Plan: {Plan}", 
                userId, rzwAmount, plan.Name);

            return (true, _localizer["Savings account created successfully"].Value, savingsAccount);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating RZW savings account. UserId: {UserId}, Amount: {Amount}", 
                userId, rzwAmount);
            return (false, _localizer["An error occurred while creating savings account"].Value, null);
        }
    }

    /// <summary>
    /// Gets user's active savings accounts
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of active savings accounts</returns>
    public async Task<List<RzwSavingsAccount>> GetUserActiveSavingsAsync(int userId)
    {
        return await _context.RzwSavingsAccounts
            .Include(s => s.Plan)
            .Where(s => s.UserId == userId && s.Status == RzwSavingsStatus.Active)
            .OrderByDescending(s => s.CreatedDate)
            .ToListAsync();
    }

    /// <summary>
    /// Gets all user's savings accounts (active and inactive)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of all savings accounts</returns>
    public async Task<List<RzwSavingsAccount>> GetUserAllSavingsAsync(int userId)
    {
        return await _context.RzwSavingsAccounts
            .Include(s => s.Plan)
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.CreatedDate)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a specific savings account for a user
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="userId">User ID</param>
    /// <returns>Savings account or null</returns>
    public async Task<RzwSavingsAccount?> GetSavingsAccountAsync(int accountId, int userId)
    {
        return await _context.RzwSavingsAccounts
            .Include(s => s.InterestPayments)
            .Include(s => s.Plan)
            .FirstOrDefaultAsync(s => s.Id == accountId && s.UserId == userId);
    }

    /// <summary>
    /// Processes early withdrawal from a savings account
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="userId">User ID (0 for admin operations)</param>
    /// <returns>Success status, message, and withdrawn amount</returns>
    public async Task<(bool Success, string Message, decimal WithdrawnAmount)> EarlyWithdrawAsync(
        int accountId, int userId)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            RzwSavingsAccount? account;

            if (userId == 0) // Admin operation
            {
                account = await _context.RzwSavingsAccounts
                    .FirstOrDefaultAsync(s => s.Id == accountId);
            }
            else // User operation
            {
                account = await GetSavingsAccountAsync(accountId, userId);
            }

            if (account == null)
                return (false, _localizer["Savings account not found"].Value, 0);

            if (account.Status != RzwSavingsStatus.Active)
                return (false, _localizer["Savings account is not active"].Value, 0);

            // Vade dolmuşsa erken çekim yapılamaz
            if (DateTime.UtcNow >= account.MaturityDate)
                return (false, _localizer["Early withdrawal is not allowed after maturity date."].Value, 0);

            // Unlock RZW balance (use account's actual userId for balance operations) using existing transaction
            var actualUserId = userId == 0 ? account.UserId : userId;
            var unlockSuccess = await _balanceService.UnlockRzwFromSavingsAsync(actualUserId, account.RzwAmount,
                $"Early withdrawal from savings account #{account.Id}", TradeType.RzwSavingsEarlyWithdrawal, _context, account.Id);

            if (!unlockSuccess)
                return (false, _localizer["Failed to unlock RZW balance"].Value, 0);

            // Calculate earned interest for early withdrawal (based on holding period)
            var earnedInterest = await _interestService.CalculateEarlyWithdrawalInterestAsync(account);

            // Calculate early withdrawal penalty using helper class (penalty is applied on earned interest, not principal)
            var penalty = RzwSavingsCalculationHelper.CalculateEarlyWithdrawalPenalty(earnedInterest, account.EarlyWithdrawalPenalty);

            // Calculate net amount user will receive (principal + interest - penalty)
            var netInterest = earnedInterest - penalty;
            var withdrawnAmount = account.RzwAmount + netInterest;

            // Add net earned interest to available balance using existing transaction (after penalty deduction)
            if (netInterest > 0)
            {
                await _balanceService.AddRzwInterestAsync(actualUserId, netInterest,
                    $"Early withdrawal interest (after penalty) from savings account #{account.Id}", _context, account.Id);

                _logger.LogInformation("Early withdrawal net interest paid. AccountId: {AccountId}, EarnedInterest: {EarnedInterest}, Penalty: {Penalty}, NetInterest: {NetInterest}",
                    account.Id, earnedInterest, penalty, netInterest);
            }
            else if (earnedInterest > 0)
            {
                _logger.LogInformation("Early withdrawal penalty equals or exceeds earned interest. AccountId: {AccountId}, EarnedInterest: {EarnedInterest}, Penalty: {Penalty}",
                    account.Id, earnedInterest, penalty);
            }

            // Update account status
            account.Status = RzwSavingsStatus.Withdrawn;
            account.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Early withdrawal completed. UserId: {UserId}, AccountId: {AccountId}, Amount: {Amount}, Penalty: {Penalty}, AdminOperation: {AdminOperation}",
                actualUserId, accountId, withdrawnAmount, penalty, userId == 0);

            return (true, _localizer["Early withdrawal completed successfully"].Value, withdrawnAmount);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error during early withdrawal. UserId: {UserId}, AccountId: {AccountId}, AdminOperation: {AdminOperation}",
                userId, accountId, userId == 0);
            return (false, _localizer["An error occurred during withdrawal"].Value, 0);
        }
    }

    /// <summary>
    /// Calculates maturity date based on start date and term
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="termType">Term type</param>
    /// <param name="termDuration">Term duration in days</param>
    /// <returns>Maturity date</returns>
    private DateTime CalculateMaturityDate(DateTime startDate, string termType, int termDuration)
    {
        return termType switch
        {
            RzwSavingsTermType.Daily => startDate.AddDays(termDuration),
            RzwSavingsTermType.Monthly => startDate.AddDays(termDuration),
            RzwSavingsTermType.Yearly => startDate.AddDays(termDuration),
            _ => startDate.AddDays(termDuration)
        };
    }

    /// <summary>
    /// Processes maturity for a savings account
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <returns>Success status and message</returns>
    public async Task<(bool Success, string Message)> ProcessMaturityAsync(int accountId)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var account = await _context.RzwSavingsAccounts
                .FirstOrDefaultAsync(s => s.Id == accountId && s.Status == RzwSavingsStatus.Active);

            if (account == null)
                return (false, "Savings account not found or not active");

            if (DateTime.UtcNow < account.MaturityDate)
                return (false, "Savings account has not matured yet");

            // Unlock RZW balance
            var unlockSuccess = await _balanceService.UnlockRzwFromSavingsAsync(account.UserId, account.RzwAmount,
                $"Maturity of savings account #{account.Id}", TradeType.RzwSavingsMaturity, null, account.Id);

            if (!unlockSuccess)
                return (false, "Failed to unlock RZW balance");

            // Update account status
            account.Status = RzwSavingsStatus.Matured;
            account.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Savings account matured. AccountId: {AccountId}, UserId: {UserId}, Amount: {Amount}",
                accountId, account.UserId, account.RzwAmount);

            return (true, "Savings account matured successfully");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error processing maturity. AccountId: {AccountId}", accountId);
            return (false, "An error occurred during maturity processing");
        }
    }

    /// <summary>
    /// Processes maturity for a savings account with interest payment and auto-renew
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <returns>Success status and message</returns>
    public async Task<(bool Success, string Message)> ProcessMaturityWithInterestAndAutoRenewAsync(int accountId)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var account = await _context.RzwSavingsAccounts
                .Include(a => a.Plan)
                .FirstOrDefaultAsync(s => s.Id == accountId && s.Status == RzwSavingsStatus.Active);

            if (account == null)
                return (false, "Savings account not found or not active");

            if (DateTime.UtcNow < account.MaturityDate)
                return (false, "Savings account has not matured yet");

            // Calculate total interest for the full term
            var totalInterest = await CalculateMaturityInterestAsync(account);

            // Pay total interest to user's available balance
            if (totalInterest > 0)
            {
                var interestWallet = await _balanceService.AddRzwInterestAsync(account.UserId, totalInterest,
                    $"Maturity interest from savings account #{account.Id}", _context, account.Id);

                if (interestWallet == null)
                {
                    await transaction.RollbackAsync();
                    return (false, "Failed to pay maturity interest");
                }

                // Create interest payment record
                var interestPayment = new RzwSavingsInterestPayment
                {
                    RzwSavingsAccountId = account.Id,
                    RzwAmount = totalInterest,
                    PaymentDate = DateTime.UtcNow,
                    Description = $"Maturity interest payment - {account.TermType} savings",
                    CreatedDate = DateTime.UtcNow,
                    // Set principal and accumulated interest for balance calculation
                    PrincipalAmount = account.RzwAmount,
                    AccumulatedInterest = account.TotalEarnedRzw + totalInterest,
                    DailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(account.InterestRate, account.TermType)
                };

                _context.RzwSavingsInterestPayments.Add(interestPayment);

                _logger.LogInformation("Maturity interest paid. AccountId: {AccountId}, Interest: {Interest:N8} RZW",
                    account.Id, totalInterest);
            }

            // Unlock original RZW amount using existing transaction
            var unlockSuccess = await _balanceService.UnlockRzwFromSavingsAsync(account.UserId, account.RzwAmount,
                $"Maturity of savings account #{account.Id}", TradeType.RzwSavingsMaturity, _context, account.Id);

            if (!unlockSuccess)
            {
                await transaction.RollbackAsync();
                return (false, "Failed to unlock RZW balance");
            }

            // Update account status
            account.Status = RzwSavingsStatus.Matured;
            account.TotalEarnedRzw = totalInterest;
            account.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            // Check for auto-renew after committing the maturity transaction
            if (account.AutoRenew && account.Plan != null && account.Plan.IsActive)
            {
                // Create new savings account with the same plan and original amount
                // This runs in a separate transaction to avoid nested transaction issues
                var newAccountResult = await CreateSavingsAccountAsync(account.UserId, account.PlanId, account.RzwAmount, account.AutoRenew);

                if (newAccountResult.Success)
                {
                    _logger.LogInformation("Auto-renewed savings account. Original AccountId: {OriginalAccountId}, New AccountId: {NewAccountId}, UserId: {UserId}",
                        account.Id, newAccountResult.Account?.Id, account.UserId);
                }
                else
                {
                    _logger.LogWarning("Failed to auto-renew savings account {AccountId}: {Message}",
                        account.Id, newAccountResult.Message);
                    // Don't fail the maturity process if auto-renew fails
                }
            }

            _logger.LogInformation("Savings account matured with interest and auto-renew check. AccountId: {AccountId}, UserId: {UserId}, Amount: {Amount:N8} RZW, Interest: {Interest:N8} RZW, AutoRenew: {AutoRenew}",
                accountId, account.UserId, account.RzwAmount, totalInterest, account.AutoRenew);

            return (true, "Savings account matured successfully with interest payment");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error processing maturity with interest and auto-renew. AccountId: {AccountId}", accountId);
            return (false, "An error occurred during maturity processing");
        }
    }

    /// <summary>
    /// Calculates total interest for maturity (compound interest for full term)
    /// Uses the shared helper class for consistent calculations
    /// </summary>
    /// <param name="account">The savings account</param>
    /// <returns>Total interest amount</returns>
    private Task<decimal> CalculateMaturityInterestAsync(RzwSavingsAccount account)
    {
        if (account.Plan == null) return Task.FromResult(0m);

        // Use the shared helper class for consistent calculations
        // This properly handles TermType (Daily, Monthly, Yearly) and applies correct rate conversion
        var totalInterest = RzwSavingsCalculationHelper.CalculateTotalInterest(
            account.RzwAmount,
            account.Plan,
            account.TermDuration);

        return Task.FromResult(totalInterest);
    }

    /// <summary>
    /// Gets matured accounts that need processing
    /// </summary>
    /// <returns>List of matured accounts</returns>
    public async Task<List<RzwSavingsAccount>> GetMaturedAccountsAsync()
    {
        return await _context.RzwSavingsAccounts
            .Where(s => s.Status == RzwSavingsStatus.Active && s.MaturityDate <= DateTime.UtcNow)
            .ToListAsync();
    }

    /// <summary>
    /// Gets accounts that need interest payment
    /// </summary>
    /// <returns>List of accounts for interest payment</returns>
    public async Task<List<RzwSavingsAccount>> GetAccountsForInterestPaymentAsync()
    {
        var today = DateTime.UtcNow.Date;

        return await _context.RzwSavingsAccounts
            .Where(s => s.Status == RzwSavingsStatus.Active &&
                       (s.LastInterestDate == null || s.LastInterestDate.Value.Date < today))
            .ToListAsync();
    }

    /// <summary>
    /// Gets accounts for admin panel with filtering and pagination
    /// </summary>
    public async Task<(List<RzwSavingsAccountViewModel> Accounts, int TotalCount)> GetAccountsForAdminAsync(
        string? searchEmail = null,
        string? status = null,
        int? planId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 50)
    {
        var query = _context.RzwSavingsAccounts
            .Include(a => a.User)
            .Include(a => a.Plan)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchEmail))
        {
            query = query.Where(a => a.User.Email.Contains(searchEmail));
        }

        if (!string.IsNullOrEmpty(status))
        {
            query = query.Where(a => a.Status == status);
        }

        if (planId.HasValue)
        {
            query = query.Where(a => a.PlanId == planId.Value);
        }

        if (startDate.HasValue)
        {
            query = query.Where(a => a.StartDate >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(a => a.StartDate <= endDate.Value);
        }

        var totalCount = await query.CountAsync();

        var accounts = await query
            .OrderByDescending(a => a.CreatedDate)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Select(a => new RzwSavingsAccountViewModel
            {
                Id = a.Id,
                UserId = a.UserId,
                UserEmail = a.User.Email,
                PlanId = a.PlanId,
                PlanName = a.Plan.Name,
                RzwAmount = a.RzwAmount,
                InterestRate = a.InterestRate,
                TermType = a.TermType,
                TermDuration = a.TermDuration,
                StartDate = a.StartDate,
                MaturityDate = a.MaturityDate,
                Status = a.Status,
                TotalEarnedRzw = a.TotalEarnedRzw,
                LastInterestDate = a.LastInterestDate,
                AutoRenew = a.AutoRenew,
                EarlyWithdrawalPenalty = a.EarlyWithdrawalPenalty,
                CreatedDate = a.CreatedDate,
                ModifiedDate = a.ModifiedDate
            })
            .ToListAsync();

        return (accounts, totalCount);
    }

    /// <summary>
    /// Gets account details for admin panel
    /// </summary>
    public async Task<RzwSavingsAccountViewModel?> GetAccountDetailsForAdminAsync(int accountId)
    {
        var account = await _context.RzwSavingsAccounts
            .Include(a => a.User)
            .Include(a => a.Plan)
            .FirstOrDefaultAsync(a => a.Id == accountId);

        if (account == null) return null;

        return new RzwSavingsAccountViewModel
        {
            Id = account.Id,
            UserId = account.UserId,
            UserEmail = account.User.Email,
            PlanId = account.PlanId,
            PlanName = account.Plan.Name,
            RzwAmount = account.RzwAmount,
            InterestRate = account.InterestRate,
            TermType = account.TermType,
            TermDuration = account.TermDuration,
            StartDate = account.StartDate,
            MaturityDate = account.MaturityDate,
            Status = account.Status,
            TotalEarnedRzw = account.TotalEarnedRzw,
            LastInterestDate = account.LastInterestDate,
            AutoRenew = account.AutoRenew,
            EarlyWithdrawalPenalty = account.EarlyWithdrawalPenalty,
            CreatedDate = account.CreatedDate,
            ModifiedDate = account.ModifiedDate
        };
    }

    /// <summary>
    /// Fixes savings accounts with zero interest rates by restoring them from their plans
    /// </summary>
    /// <returns>Number of accounts fixed</returns>
    public async Task<(int FixedCount, string Message)> FixZeroInterestRateAccountsAsync()
    {
        try
        {
            // Find accounts with zero interest rates
            var problematicAccounts = await _context.RzwSavingsAccounts
                .Include(a => a.Plan)
                .Where(a => a.InterestRate == 0 && a.Status == RzwSavingsStatus.Active)
                .ToListAsync();

            if (!problematicAccounts.Any())
            {
                return (0, "No accounts with zero interest rates found");
            }

            int fixedCount = 0;
            var fixedAccountIds = new List<int>();

            foreach (var account in problematicAccounts)
            {
                if (account.Plan != null && account.Plan.InterestRate > 0)
                {
                    // Restore interest rate from plan
                    account.InterestRate = account.Plan.InterestRate;
                    account.ModifiedDate = DateTime.UtcNow;
                    fixedCount++;
                    fixedAccountIds.Add(account.Id);

                    _logger.LogInformation("Fixed zero interest rate for account {AccountId}. Restored rate: {InterestRate}",
                        account.Id, account.Plan.InterestRate);
                }
                else
                {
                    _logger.LogWarning("Cannot fix account {AccountId} - plan not found or plan has zero interest rate",
                        account.Id);
                }
            }

            if (fixedCount > 0)
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("Fixed {FixedCount} savings accounts with zero interest rates. Account IDs: {AccountIds}",
                    fixedCount, string.Join(", ", fixedAccountIds));
            }

            return (fixedCount, $"Fixed {fixedCount} accounts with zero interest rates");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fixing zero interest rate accounts");
            return (0, $"Error fixing accounts: {ex.Message}");
        }
    }

    /// <summary>
    /// Updates the AutoRenew property of a savings account (account ownership must be checked by caller)
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="autoRenew">New AutoRenew value</param>
    /// <returns>Success status and message</returns>
    public async Task<(bool Success, string Message)> UpdateAutoRenewAsync(int accountId, bool autoRenew)
    {
        var account = await _context.RzwSavingsAccounts.FirstOrDefaultAsync(a => a.Id == accountId);

        if (account == null)
            return (false, _localizer["Savings account not found."].Value);

        if (account.Status != RzwSavingsStatus.Active)
            return (false, _localizer["Only active savings accounts can be updated."].Value);

        account.AutoRenew = autoRenew;
        account.ModifiedDate = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return (true, _localizer["Auto-renew setting updated successfully."].Value);
    }

    /// <summary>
    /// Gets all savings accounts for a user (including inactive ones)
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>List of all savings accounts</returns>
    public async Task<List<RzwSavingsAccount>> GetUserSavingsAsync(int userId)
    {
        return await _context.RzwSavingsAccounts
            .Include(a => a.Plan)
            .Where(a => a.UserId == userId)
            .OrderByDescending(a => a.CreatedDate)
            .ToListAsync();
    }

    /// <summary>
    /// Gets savings account by ID without user validation (for admin operations)
    /// </summary>
    /// <param name="accountId">The account ID</param>
    /// <returns>The savings account if found</returns>
    public async Task<RzwSavingsAccount?> GetSavingsAccountByIdAsync(int accountId)
    {
        return await _context.RzwSavingsAccounts
            .Include(a => a.Plan)
            .Include(a => a.User)
            .FirstOrDefaultAsync(a => a.Id == accountId);
    }
}
