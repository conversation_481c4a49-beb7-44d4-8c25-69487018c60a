-- Update existing RZW_SAVINGS_INTEREST_PAYMENT records with missing fields
-- This script fills in PRINCIPAL_AMOUNT, ACCUMULATED_INTEREST, and DAILY_RATE for existing records

-- First, let's see what we have
SELECT 
    ip.ID,
    ip.RZW_SAVINGS_ACCOUNT_ID,
    ip.RZW_AMOUNT,
    ip.PAYMENT_DATE,
    ip.PRINCIPAL_AMOUNT,
    ip.ACCUMULATED_INTEREST,
    ip.DAILY_RATE,
    sa.RZW_AMOUNT as ACCOUNT_PRINCIPAL,
    sa.TOTAL_EARNED_RZW,
    sa.INTEREST_RATE as ACCOUNT_INTEREST_RATE,
    sa.TERM_TYPE,
    sp.INTEREST_RATE as PLAN_INTEREST_RATE,
    sp.TERM_TYPE as PLAN_TERM_TYPE
FROM RZW_SAVINGS_INTEREST_PAYMENT ip
INNER JOIN RZW_SAVINGS_ACCOUNT sa ON ip.RZW_SAVINGS_ACCOUNT_ID = sa.ID
LEFT JOIN RZW_SAVINGS_PLAN sp ON sa.PLAN_ID = sp.ID
WHERE ip.PRINCIPAL_AMOUNT IS NULL 
   OR ip.ACCUMULATED_INTEREST IS NULL 
   OR ip.DAILY_RATE IS NULL
ORDER BY ip.PAYMENT_DATE;

-- Update PRINCIPAL_AMOUNT (always the account's initial RZW amount)
UPDATE RZW_SAVINGS_INTEREST_PAYMENT 
SET PRINCIPAL_AMOUNT = (
    SELECT sa.RZW_AMOUNT 
    FROM RZW_SAVINGS_ACCOUNT sa 
    WHERE sa.ID = RZW_SAVINGS_INTEREST_PAYMENT.RZW_SAVINGS_ACCOUNT_ID
)
WHERE PRINCIPAL_AMOUNT IS NULL;

-- Update ACCUMULATED_INTEREST 
-- This is trickier - we need to calculate the cumulative interest up to each payment
-- For now, we'll use a simple approach: sum all previous payments + current payment
UPDATE RZW_SAVINGS_INTEREST_PAYMENT 
SET ACCUMULATED_INTEREST = (
    SELECT SUM(ip2.RZW_AMOUNT)
    FROM RZW_SAVINGS_INTEREST_PAYMENT ip2
    WHERE ip2.RZW_SAVINGS_ACCOUNT_ID = RZW_SAVINGS_INTEREST_PAYMENT.RZW_SAVINGS_ACCOUNT_ID
      AND ip2.PAYMENT_DATE <= RZW_SAVINGS_INTEREST_PAYMENT.PAYMENT_DATE
)
WHERE ACCUMULATED_INTEREST IS NULL;

-- Update DAILY_RATE based on plan type
-- Daily plans: use rate directly
UPDATE RZW_SAVINGS_INTEREST_PAYMENT 
SET DAILY_RATE = (
    SELECT CASE sa.TERM_TYPE
        WHEN 'Daily' THEN sa.INTEREST_RATE
        WHEN 'Monthly' THEN sa.INTEREST_RATE / 30.0
        WHEN 'Yearly' THEN sa.INTEREST_RATE / 365.0
        ELSE 0
    END
    FROM RZW_SAVINGS_ACCOUNT sa 
    WHERE sa.ID = RZW_SAVINGS_INTEREST_PAYMENT.RZW_SAVINGS_ACCOUNT_ID
)
WHERE DAILY_RATE IS NULL;

-- If account doesn't have TERM_TYPE, try to get it from the plan
UPDATE RZW_SAVINGS_INTEREST_PAYMENT 
SET DAILY_RATE = (
    SELECT CASE sp.TERM_TYPE
        WHEN 'Daily' THEN sp.INTEREST_RATE
        WHEN 'Monthly' THEN sp.INTEREST_RATE / 30.0
        WHEN 'Yearly' THEN sp.INTEREST_RATE / 365.0
        ELSE 0
    END
    FROM RZW_SAVINGS_ACCOUNT sa 
    INNER JOIN RZW_SAVINGS_PLAN sp ON sa.PLAN_ID = sp.ID
    WHERE sa.ID = RZW_SAVINGS_INTEREST_PAYMENT.RZW_SAVINGS_ACCOUNT_ID
)
WHERE DAILY_RATE IS NULL OR DAILY_RATE = 0;

-- Verify the updates
SELECT 
    ip.ID,
    ip.RZW_SAVINGS_ACCOUNT_ID,
    ip.RZW_AMOUNT,
    ip.PAYMENT_DATE,
    ip.PRINCIPAL_AMOUNT,
    ip.ACCUMULATED_INTEREST,
    ip.DAILY_RATE,
    sa.RZW_AMOUNT as ACCOUNT_PRINCIPAL,
    sa.TOTAL_EARNED_RZW,
    sa.TERM_TYPE
FROM RZW_SAVINGS_INTEREST_PAYMENT ip
INNER JOIN RZW_SAVINGS_ACCOUNT sa ON ip.RZW_SAVINGS_ACCOUNT_ID = sa.ID
ORDER BY ip.RZW_SAVINGS_ACCOUNT_ID, ip.PAYMENT_DATE;
